require 'test_helper'

class SepayServiceTest < ActiveSupport::TestCase
  def setup
    @sepay_service = SepayService.new
    @exam_form = ExamRegistrationForm.new(
      firstname: "Test",
      lastname: "User",
      phone_number: "**********",
      exam_day: "01/01/2025",
      exam_time_range: "Sáng (8:00 - 11:00)",
      payment_amount: 1_600_000
    )
  end

  test "should generate correct QR URL format" do
    payment_info = @sepay_service.generate_payment_qr(@exam_form)

    assert_not_nil payment_info[:qr_url]
    assert_includes payment_info[:qr_url], "https://qr.sepay.vn/img?"
    assert_includes payment_info[:qr_url], "acc=*************"
    assert_includes payment_info[:qr_url], "bank=Agribank"
    assert_includes payment_info[:qr_url], "amount=1600000"
    assert_includes payment_info[:qr_url], "des=VEPT"
  end

  test "should generate payment code with correct format" do
    payment_info = @sepay_service.generate_payment_qr(@exam_form)

    assert_not_nil payment_info[:payment_code]
    assert_includes payment_info[:payment_code], "VEPT"
    assert_includes payment_info[:payment_code], "20250101AM"
    assert_includes payment_info[:payment_code], "**********"
    assert_includes payment_info[:payment_code], "TestUser"
  end

  test "should return bank info" do
    payment_info = @sepay_service.generate_payment_qr(@exam_form)

    assert_not_nil payment_info[:bank_info]
    assert_equal "*************", payment_info[:bank_info][:account_number]
    assert_equal "CTY TNHH DT VA PT GD CANH BUOM", payment_info[:bank_info][:account_name]
    assert_includes payment_info[:bank_info][:bank_name], "Agribank"
  end
end
