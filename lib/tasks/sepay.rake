namespace :sepay do
  desc "Check payment status for pending exam registrations"
  task check_payments: :environment do
    puts "Checking payment status for pending exam registrations..."

    sepay_service = SepayService.new
    pending_registrations = ExamRegistrationForm.where(payment_status: :pending)
                                                .where('created_at > ?', 7.days.ago)

    puts "Found #{pending_registrations.count} pending registrations"

    pending_registrations.each do |registration|
      next if registration.sepay_payment_code.blank?

      puts "Checking payment for registration ID: #{registration.id}"

      transaction = sepay_service.check_transaction_status(registration.sepay_payment_code)

      if transaction
        puts "Payment found for registration ID: #{registration.id}"
        registration.mark_as_paid!(transaction['id'])
        UserMailer.with(user: registration).payment_confirmation.deliver_later
        puts "Updated registration ID: #{registration.id} to paid status"
      else
        puts "No payment found for registration ID: #{registration.id}"
      end

      # Delay to avoid rate limiting
      sleep(1)
    end

    puts "Payment check completed"
  end

  desc "Test Sepay service connection"
  task test_connection: :environment do
    puts "Testing Sepay service connection..."

    # <PERSON><PERSON><PERSON> tra c<PERSON>u hình
    puts "Sepay Settings:"
    puts "  API Token: #{Settings.sepay.api_token.present? ? 'Present' : 'Missing'}"
    puts "  Account Number: #{Settings.sepay.account_number}"
    puts "  Bank Name: #{Settings.sepay.bank_name}"
    puts "  Webhook URL: #{Settings.sepay.webhook_url}"
    puts "  Debug Mode: #{Settings.sepay.debug}"
    puts ""

    sepay_service = SepayService.new

    # Test tạo QR code
    puts "Testing QR code generation..."
    test_registration = ExamRegistrationForm.new(
      firstname: "Test",
      lastname: "User",
      phone_number: "**********",
      exam_day: "01/01/2025",
      exam_time_range: "Sáng (8:00 - 11:00)",
      payment_amount: Settings.sepay.payment_amount
    )

    payment_info = sepay_service.generate_payment_qr(test_registration)
    puts "QR URL: #{payment_info[:qr_url]}"
    puts "Payment Code: #{payment_info[:payment_code]}"
    puts "Amount: #{payment_info[:amount]}"

    puts "Sepay service test completed"
  end
end
