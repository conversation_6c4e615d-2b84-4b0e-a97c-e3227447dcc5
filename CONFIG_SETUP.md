# Cấu hình Sepay với gem Config

## Tổng quan
Dự án sử dụng gem `config` để quản lý cấu hình Sepay một cách dễ dàng và an toàn.

## Cấu trúc file cấu hình

```
config/
├── settings.yml                 # Cấu hình chung cho tất cả environments
├── settings/
│   ├── development.yml          # Cấu hình cho development
│   ├── production.yml           # Cấu hình cho production
│   └── test.yml                 # Cấu hình cho test
└── initializers/
    └── config.rb                # Khởi tạo gem config
```

## Cách sử dụng

### 1. Truy cập cấu hình trong code:
```ruby
# Trong Rails application
Settings.sepay.api_token
Settings.sepay.account_number
Settings.sepay.bank_name
```

### 2. Kiểm tra cấu hình:
```bash
# Rails console
rails console

# Xem tất cả cấu hình Sepay
Settings.sepay

# Xem cấu hình cụ thể
Settings.sepay.api_token
```

### 3. Test cấu hình:
```bash
# Chạy rake task để test
rails sepay:test_connection
```

## Ưu điểm của gem Config

1. **Tự động load theo environment**: Development/Production/Test có cấu hình riêng
2. **Merge cấu hình**: Settings chung + settings theo environment
3. **Truy cập dễ dàng**: `Settings.sepay.api_token` thay vì hash phức tạp
4. **An toàn**: Không cần lo lắng về việc file config không tồn tại
5. **Hỗ trợ ENV variables**: Có thể override bằng environment variables

## Environment Variables (Optional)

Có thể sử dụng ENV variables để override:
```bash
export SETTINGS__SEPAY__API_TOKEN="your_token_here"
export SETTINGS__SEPAY__ACCOUNT_NUMBER="your_account"
```

## Lưu ý bảo mật

- **KHÔNG** commit API token thực vào Git
- Sử dụng placeholder trong production.yml
- Set API token thực trên server production
- Có thể sử dụng Rails credentials hoặc ENV variables cho production
