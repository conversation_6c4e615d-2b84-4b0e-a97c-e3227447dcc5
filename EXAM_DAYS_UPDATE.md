# Hướng dẫn cập nhật Exam Days

## Tổng quan
C<PERSON><PERSON> rake tasks để quản lý và cập nhật ngày thi từ năm 2024 sang 2025 và ngược lại.

## Các rake tasks có sẵn

### 1. <PERSON><PERSON><PERSON> tra thống kê hiện tại
```bash
rails exam_days:stats
```

**Chức năng:** Hiển thị thống kê số lượng ngày thi theo năm trong:
- File YAML (`db/master_data/exam_days.yml`)
- Database (`Master::ExamDay`)
- Exam Registration Forms (`ExamRegistrationForm`)

**Output mẫu:**
```
Exam Days Statistics:
==================================================
YAML File (exam_days.yml):
  - 2024 dates: 31
  - 2025 dates: 0

Database (Master::ExamDay):
  - 2024 dates: 31
  - 2025 dates: 0

Exam Registration Forms:
  - 2024 dates: 5
  - 2025 dates: 0
==================================================
```

### 2. Cập nhật từ 2024 sang 2025
```bash
rails exam_days:update_to_2025
```

**Chức năng:** Cập nhật tất cả ngày thi từ năm 2024 sang 2025:
- Cập nhật file `exam_days.yml`
- Cập nhật records trong bảng `Master::ExamDay`
- Cập nhật records trong bảng `ExamRegistrationForm`

**Ví dụ chuyển đổi:**
- `2024-08-25` → `2025-08-25`
- `25/08/2024` → `25/08/2025`

### 3. Rollback từ 2025 về 2024
```bash
rails exam_days:rollback_to_2024
```

**Chức năng:** Rollback tất cả ngày thi từ năm 2025 về 2024:
- Rollback file `exam_days.yml`
- Rollback records trong bảng `Master::ExamDay`
- Rollback records trong bảng `ExamRegistrationForm`

### 4. Cập nhật từ năm bất kỳ
```bash
rails exam_days:update_to_year[from_year,to_year]
```

**Ví dụ:**
```bash
rails exam_days:update_to_year[2024,2025]
rails exam_days:update_to_year[2025,2026]
```

## Quy trình thực hiện

### Bước 1: Kiểm tra trạng thái hiện tại
```bash
rails exam_days:stats
```

### Bước 2: Backup dữ liệu (khuyến nghị)
```bash
# Backup database
pg_dump your_database > backup_before_update.sql

# Backup file YAML
cp db/master_data/exam_days.yml db/master_data/exam_days.yml.backup
```

### Bước 3: Thực hiện cập nhật
```bash
rails exam_days:update_to_2025
```

### Bước 4: Kiểm tra kết quả
```bash
rails exam_days:stats
```

### Bước 5: Reload master data (nếu cần)
```bash
rails db:seed
```

## Lưu ý quan trọng

### ⚠️ **Cảnh báo:**
- **Luôn backup dữ liệu** trước khi chạy các tasks này
- Tasks này sẽ **thay đổi trực tiếp** file YAML và database
- Kiểm tra kỹ kết quả sau khi chạy

### 📋 **Các file/bảng bị ảnh hưởng:**
1. `db/master_data/exam_days.yml`
2. Bảng `master_exam_days`
3. Bảng `exam_registration_forms`

### 🔄 **Format dữ liệu:**
- **File YAML:** `YYYY-MM-DD` (ví dụ: `2025-08-25`)
- **Database Master::ExamDay:** `Date` object
- **ExamRegistrationForm:** `DD/MM/YYYY` string (ví dụ: `25/08/2025`)

## Troubleshooting

### Lỗi: "exam_days.yml file not found"
```bash
# Kiểm tra file có tồn tại không
ls -la db/master_data/exam_days.yml

# Tạo lại file nếu cần
touch db/master_data/exam_days.yml
```

### Lỗi: "Master::ExamDay not found"
```bash
# Chạy migration và seed
rails db:migrate
rails db:seed
```

### Kiểm tra dữ liệu sau khi update
```bash
# Rails console
rails console

# Kiểm tra Master::ExamDay
Master::ExamDay.where("date >= ?", Date.new(2025, 1, 1)).count

# Kiểm tra ExamRegistrationForm
ExamRegistrationForm.where("exam_day LIKE ?", "%/2025").count
```

## Rollback nếu có lỗi

Nếu có vấn đề sau khi update, có thể rollback:

```bash
# Rollback về 2024
rails exam_days:rollback_to_2024

# Hoặc restore từ backup
cp db/master_data/exam_days.yml.backup db/master_data/exam_days.yml
psql your_database < backup_before_update.sql
```
