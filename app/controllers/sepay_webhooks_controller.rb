class SepayWebhooksController < ApplicationController
  skip_before_action :verify_authenticity_token
  before_action :verify_sepay_ip

    def receive
    # Webhook thanh toán đã bị vô hiệu hóa
    # Chỉ sử dụng import giao dịch thủ công
    Rails.logger.info "Webhook thanh toán đã bị vô hiệu hóa. Sử dụng import giao dịch thủ công."

    render json: {
      success: false,
      message: 'Webhook thanh toán đã bị vô hiệu hóa. Vui lòng sử dụng import giao dịch thủ công.'
    }, status: :ok
  end

  private

  def verify_sepay_ip
    # Webhook đã bị vô hiệu hóa, nhưng giữ lại method để tương thích
    Rails.logger.info "Webhook verification bỏ qua - webhook đã bị vô hiệu hóa"
  end
end
