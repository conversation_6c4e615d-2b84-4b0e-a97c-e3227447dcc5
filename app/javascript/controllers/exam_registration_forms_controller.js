import { Controller } from "@hotwired/stimulus";

function checkLeapYear(year) {
  if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) return 1;
  return 0;
}
export default class extends Controller {
  static targets = [
    "form",
    "examCenter",
    "examDay",
    "examSession",
    "college",
    "identityDocumentType",
    "registrationReason",
    "supportChoice",
    "certificateDeliveryMethod",
    "birthDay",
    "birthMonth",
    "birthYear",
    "thankYou",
    "submitButton",
  ];
  static values = {
    masterColleges: Array,
    masterExamCenters: Array,
    masterExamDays: Array,
    masterExamSessions: Array,
    masterIdDocumentTypes: Array,
    masterRegistrationReasons: Array,
    masterSupportChoices: Array,
    masterCertificateDeliveryMethods: Array,
    listExamCenter: Array,
    listExamSession: Array,
    listExamDay: Array,
    listBirthDay: Array,
    listBirthMonth: Array,
    listBirthYear: Array,
    testData: Object,
  };
  initialize() {
    for (let i = 1; i <= 31; i++)
      this.listBirthDayValue = [
        ...this.listBirthDayValue,
        {
          value: `${i < 10 ? "0" : ""}${i}`,
          label: `${i < 10 ? "0" : ""}${i}`,
        },
      ];
    for (let i = 1; i <= 12; i++)
      this.listBirthMonthValue = [
        ...this.listBirthMonthValue,
        {
          value: `${i < 10 ? "0" : ""}${i}`,
          label: `${i < 10 ? "0" : ""}${i}`,
        },
      ];
    let currentYear = new Date().getFullYear();
    for (let i = currentYear - 12; i >= currentYear - 80; i--)
      this.listBirthYearValue = [
        ...this.listBirthYearValue,
        {
          value: `${i}`,
          label: `${i}`,
        },
      ];
  }
  connect() {
    this.generateOptions(this.birthDayTarget, this.listBirthDayValue);
    this.generateOptions(this.birthMonthTarget, this.listBirthMonthValue);
    this.generateOptions(this.birthYearTarget, this.listBirthYearValue);
    this.generateOptions(this.examCenterTarget, this.masterExamCentersValue);
    this.generateOptions(this.collegeTarget, this.masterCollegesValue);
    this.generateOptions(
      this.identityDocumentTypeTarget,
      this.masterIdDocumentTypesValue
    );
    this.generateOptions(
      this.registrationReasonTarget,
      this.masterRegistrationReasonsValue
    );
    this.generateOptions(
      this.supportChoiceTarget,
      this.masterSupportChoicesValue
    );
    this.generateOptions(this.certificateDeliveryMethodTarget, this.masterCertificateDeliveryMethodsValue);
    
    // Auto fill test data if available (commented out for manual control)
    // if (this.testDataValue && Object.keys(this.testDataValue).length > 0) {
    //   setTimeout(() => this.fillTestData(), 500);
    // }
  }
  clearExamDayChoice() {
    $(this.examDayTarget).find("#NgayThi").html("");
    $(this.examDayTarget).find(".DropDownList ul").html("");
    $(this.examDayTarget).find(".DropDown input").val("");
  }

  clearExamSessionChoice() {
    $(this.examSessionTarget).find("#CaThi").html("");
    $(this.examSessionTarget).find(".DropDown input").val("");
    $(this.examSessionTarget).find(".DropDownList ul span").remove();
  }
  changeExamCenter() {
    const examCenterID = parseInt(
      $(this.examCenterTarget).find("option").attr("data-id")
    );
    this.clearExamDayChoice();
    this.clearExamSessionChoice();
    this.listExamDayValue = this.masterExamDaysValue
      .filter((exam_day) => exam_day.parent_id === examCenterID)
      .map((exam_day) => ({
        parent_id: exam_day.parent_id,
        id: exam_day.id,
        label: exam_day.label,
        value: exam_day.value,
      }));
    this.generateOptions(this.examDayTarget, this.listExamDayValue);
  }

  changeExamDay() {
    const examDayID = parseInt(
      $(this.examDayTarget).find("option").attr("data-id")
    );
    this.clearExamSessionChoice();
    this.listExamSessionValue = this.masterExamSessionsValue
      .filter((exam_session) => exam_session.parent_id === examDayID)
      .map((exam_session) => ({
        parent_id: exam_session.parent_id,
        id: exam_session.id,
        label: exam_session.label,
        value: exam_session.value,
      }));
    this.generateOptions(this.examSessionTarget, this.listExamSessionValue);
  }

  selectOption($li) {
    $li.parent().find("li").removeClass("ONSelect");
    $li.addClass("ONSelect");
    $li.parent().parent().parent().removeClass("ListDown");
    $li.parent().parent().parent().find(".DropDownListInput input").val("");
    $li.parent().find("li").css("display", "block");
    if (
      $.trim(
        $li.parent().parent().parent().find(".DropDownInput input").val()
      ) !== $.trim($li.text())
    ) {
      $li
        .parent()
        .parent()
        .parent()
        .find(".DropDownInput input")
        .val($.trim($li.text()))
        .change();
      $li
        .parent()
        .parent()
        .parent()
        .prev()
        .html(
          `<option value="${$li.attr("data-value")}" data-parent-id="${$li.attr(
            "data-parent-id"
          )}" data-id="${$li.attr("data-id")}"> ${$.trim($li.text())} </option>`
        );
    }
    const inputName = $li
      .parent()
      .parent()
      .parent()
      .parent()
      .find("select")
      .get(0).name;
    if (inputName === "exam_registration_form[exam_center_name]")
      this.changeExamCenter();
    else if (inputName === "exam_registration_form[exam_day]")
      this.changeExamDay();
  }
  generateOptions(target, listOptions) {
    let listHTML = "";
    listOptions.forEach((option) => {
      listHTML += `<li data-value="${option.value}" data-parent-id="${
        option?.parent_id ?? ""
      }" data-id="${option?.id ?? ""}">${option.label}</li>`;
    });
    $(target).find("ul").html(listHTML);
    $(target)
      .find("ul li")
      .on("click", (event) => {
        this.selectOption($(event.currentTarget));
      });
  }

  validateInputs() {
    let check = true;
    var ignore_id = [
      "TruongDangCongTac",
      "CtyDangCongTac",
      "HoTroKhoKhan",
      "LyDoKhac",
      "DiaChiChuyenPhat",
      "KhoKhanKhac",
    ];
    $(this.formTarget)
      .find(
        '.formBody > input[type="text"], .formBody > input[type="date"], .formBody > textarea'
      )
      .each(function () {
        if (jQuery.inArray($(this).attr("id"), ignore_id) !== -1) {
        } else {
          if ($(this).val() == "") {
            $(this).parent().parent().parent().addClass("rsform-error");
            check = false;
          } else {
            $(this).parent().parent().parent().removeClass("rsform-error");
          }
        }
      });
    $(this.formTarget)
      .find(".formBody > select")
      .each(function () {
        if (jQuery.inArray($(this).attr("id"), ignore_id) !== -1) {
        } else {
          if ($(this).find("option:selected").length < 1) {
            $(this).parent().parent().parent().addClass("rsform-error");
            check = false;
          } else {
            $(this).parent().parent().parent().removeClass("rsform-error");
          }
        }
      });
    $(this.formTarget)
      .find(".rsform-block-gioitinh, .rsform-block-xacnhan, .rsform-block-capdodangki")
      .each(function () {
        if ($(this).find("input:checked").length > 0) {
          $(this).removeClass("rsform-error");
        } else {
          $(this).addClass("rsform-error");
          check = false;
        }
      });
    return check;
  }

  disableSubmitButton() {
    this.submitButtonTarget.disabled = true;
    this.submitButtonTarget.value = "Chờ một chút...";
  }

  enableSubmitButton() {
    this.submitButtonTarget.disabled = false;
    this.submitButtonTarget.value = "Đăng ký dự thi";
  }

  // Format số tiền theo chuẩn Việt Nam
  formatCurrency(amount) {
    if (!amount || amount === 0) return "0 đ";

    // Chuyển về integer để loại bỏ phần thập phân .0
    const amountInt = parseInt(amount);
    // Format với dấu chấm phân cách hàng nghìn
    return amountInt.toLocaleString('vi-VN') + ' đ';
  }

  showThankYou(formInformation, paymentInfo = null) {
    const { phone_number, firstname, lastname, exam_day, exam_time_range } =
      formInformation;
    $(this.formTarget).css("display", "none");
    $("#Mod398 .workshome-title").css("display", "none");
    $(this.thankYouTarget).css("display", "block");

    if (paymentInfo) {
      // Hiển thị thông tin thanh toán Sepay
      $(this.thankYouTarget).find("#transfer-content").html(paymentInfo.payment_code);
      $(this.thankYouTarget).find("#payment-amount").html(this.formatCurrency(paymentInfo.amount));

      // Hiển thị QR Code nếu có
      if (paymentInfo.qr_url) {
        $(this.thankYouTarget).find("#qr-code-container").html(`
          <div class="text-center mb-3">
            <h5>Quét mã QR để thanh toán:</h5>
            <img src="${paymentInfo.qr_url}" alt="QR Code thanh toán" class="img-fluid" style="max-width: 300px;">
          </div>
        `);
      }
    } else {
      // Fallback về cách cũ
      const examTime = `${exam_day.split("/").join("")}${
        exam_time_range.includes("Sáng") ? "AM" : "PM"
      }`;
      const userName = (lastname + firstname).replace(/\s+/g, "");
      const transferContent = `VEPT ${examTime} ${phone_number} ${userName}`;
      $(this.thankYouTarget).find("#transfer-content").html(transferContent);
    }
  }

  submit(event) {
    event.preventDefault();
    const validation = this.validateInputs();
    if (validation === false) {
      alert("Vui lòng nhập đầy đủ thông tin!");
      return;
    }

    // Disable submit button
    this.disableSubmitButton();
    let formData = new FormData(this.formTarget);
    const birth_day = formData.get("exam_registration_form[birth_day]");
    const birth_month = formData.get("exam_registration_form[birth_month]");
    const birth_year = formData.get("exam_registration_form[birth_year]");
    formData.set(
      "exam_registration_form[birthday]",
      `${birth_day}/${birth_month}/${birth_year}`
    );
    formData.delete("exam_registration_form[birth_day]");
    formData.delete("exam_registration_form[birth_month]");
    formData.delete("exam_registration_form[birth_year]");

    formData.set(
      "exam_registration_form[gender]",
      parseInt(formData.get("exam_registration_form[gender]"), 10)
    );
    formData.set(
      "exam_registration_form[work_location_type]",
      parseInt(formData.get("exam_registration_form[work_location_type]"), 10)
    );
    formData.set(
      "exam_registration_form[id_expiry_date]",
      new Date(formData.get("exam_registration_form[id_expiry_date]"))
    );

    $.ajax({
      url: this.formTarget.action,
      type: "POST",
      data: formData,
      processData: false,
      contentType: false,
      success: (response) => {
        let formInformation = {};
        formData.forEach((value, key) => {
          if (key.startsWith("exam_registration_form")) {
            const fieldName = key.slice(23, -1);
            formInformation[fieldName] = value;
          }
        });
        // Kiểm tra xem response có payment_info không
        const paymentInfo = response && response.payment_info ? response.payment_info : null;
        this.showThankYou(formInformation, paymentInfo);
        // Note: Don't re-enable button here since form is hidden after success
      },
      error: (error) => {
        // Re-enable submit button on error
        this.enableSubmitButton();
        alert(error.responseJSON.errors.join(', '));
      },
    });
  }

  fillTestData() {
    const testData = this.testDataValue;
    
    if (!testData || Object.keys(testData).length === 0) {
      alert('Không có test data để fill!');
      return;
    }
    
    // Fill text inputs
    if (testData.lastname) {
      $('#Ho').val(testData.lastname);
    }
    if (testData.firstname) {
      $('#Ten').val(testData.firstname);
    }
    if (testData.id_document_number) {
      $('#SoGiayTo').val(testData.id_document_number);
    }
    if (testData.id_expiry_date) {
      $('#NgayHetHan').val(testData.id_expiry_date);
    }
    if (testData.id_issue_place) {
      $('#NoiCap').val(testData.id_issue_place);
    }
    if (testData.email) {
      $('#Email').val(testData.email);
    }
    if (testData.phone_number) {
      $('#SoDienThoai').val(testData.phone_number);
    }
    if (testData.detailed_contact_address) {
      $('#DiaChiLienHe').val(testData.detailed_contact_address);
    }
    if (testData.company_name) {
      $('#CtyDangCongTac').val(testData.company_name);
    }

    // Fill radio buttons
    if (testData.gender) {
      $(`input[name="exam_registration_form[gender]"][value="${testData.gender}"]`).prop('checked', true);
    }
    if (testData.work_location_type) {
      $(`input[name="exam_registration_form[work_location_type]"][value="${testData.work_location_type}"]`).prop('checked', true);
    }
    if (testData.exam_level) {
      $(`input[name="exam_registration_form[exam_level]"][value="${testData.exam_level}"]`).prop('checked', true);
    }
    
    // Fill checkbox
    if (testData.need_specific_support !== undefined) {
      $('#GapKhoKhan0').prop('checked', testData.need_specific_support);
    }
    
    // Fill confirmation checkbox
    $('#XacNhan0').prop('checked', true);

    // Fill dropdown selections
    this.fillDropdownSelection(this.birthDayTarget, testData.birth_day);
    this.fillDropdownSelection(this.birthMonthTarget, testData.birth_month);
    this.fillDropdownSelection(this.birthYearTarget, testData.birth_year);
    this.fillDropdownSelection(this.collegeTarget, testData.college_name);
    this.fillDropdownSelection(this.identityDocumentTypeTarget, testData.id_document_type);
    this.fillDropdownSelection(this.registrationReasonTarget, testData.registration_reason);
    this.fillDropdownSelection(this.certificateDeliveryMethodTarget, testData.certificate_delivery_method);
    
    // Fill exam center first, then exam day and session with delays
    if (testData.exam_center_name) {
      this.fillDropdownSelection(this.examCenterTarget, testData.exam_center_name);
      
      // Fill exam day after exam center is selected
      setTimeout(() => {
        const firstExamDay = this.masterExamDaysValue.find(day => 
          this.masterExamCentersValue.find(center => 
            center.value === testData.exam_center_name
          )?.id === day.parent_id
        );
        if (firstExamDay) {
          this.fillDropdownSelection(this.examDayTarget, firstExamDay.value);
          
          // Fill exam session after exam day is selected
          setTimeout(() => {
            const firstExamSession = this.masterExamSessionsValue.find(session => 
              session.parent_id === firstExamDay.id
            );
            if (firstExamSession) {
              this.fillDropdownSelection(this.examSessionTarget, firstExamSession.value);
            }
          }, 300);
                 }
       }, 200);
     }
   }

  fillDropdownSelection(target, value) {
    if (!value) return;
    
    setTimeout(() => {
      const $li = $(target).find(`ul li[data-value="${value}"]`);
      if ($li.length > 0) {
        this.selectOption($li);
      }
    }, 100);
  }
}
