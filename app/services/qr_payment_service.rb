class QrPaymentService
  def initialize
    # Chỉ load settings cần thiết cho QR code
    @account_number = Settings.sepay.account_number
    @account_name = Settings.sepay.account_name
    @bank_name = Settings.sepay.bank_name
    @bank_full_name = Settings.sepay.bank_full_name
    @branch = Settings.sepay.branch
    @qr_base_url = Settings.sepay.qr_base_url

    Rails.logger.info "QR Service initialized for account: #{@account_number}"
  end

  # Tạo QR Code thanh toán
  def generate_payment_qr(exam_registration_form)
    payment_code = exam_registration_form.generate_payment_code
    amount = exam_registration_form.get_exam_center_fee

    # Lưu payment code vào database
    exam_registration_form.update(sepay_payment_code: payment_code)

    # Tạo URL QR Code theo format của Sepay
    # https://qr.sepay.vn/img?acc=SO_TAI_KHOAN&bank=NGAN_HANG&amount=SO_TIEN&des=NOI_DUNG
    qr_params = {
      acc: @account_number,
      bank: @bank_name, # Sử dụng short_name từ Sepay API (Agribank)
      amount: amount.to_i,
      des: payment_code
    }

    qr_url = "#{@qr_base_url}?#{qr_params.to_query}"

    Rails.logger.info "Generated QR URL: #{qr_url}" if Settings.sepay.debug

    result = {
      qr_url: qr_url,
      payment_code: payment_code,
      amount: amount,
      bank_info: {
        bank_name: @bank_full_name,
        account_number: @account_number,
        account_name: @account_name,
        branch: @branch
      }
    }

    Rails.logger.info "Generated QR code for payment: #{payment_code}"

    result
  end

      # Method này không còn sử dụng vì đã bỏ SePay API
  # Chỉ giữ lại để tương thích, luôn trả về false
  def check_transaction_status(payment_code, required_amount = nil)
    Rails.logger.info "API thanh toán đã bị vô hiệu hóa. Sử dụng import giao dịch thủ công."
    false
  end

  # Method này không còn sử dụng vì đã bỏ webhook
  # Chỉ giữ lại để tương thích, luôn trả về false
  def process_webhook(webhook_data)
    Rails.logger.info "Webhook thanh toán đã bị vô hiệu hóa. Sử dụng import giao dịch thủ công."
    false
  end
end
