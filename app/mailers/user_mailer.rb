class UserMailer < ApplicationMailer
  def registration_confirmation
    @user = params[:user]
    @user_email = @user.email
    mail(from: '<EMAIL>', to: @user_email, subject: 'Thông báo đăng ký lịch thi')
  end

  def payment_confirmation
    @user = params[:user]
    @user_email = @user.email
    mail(from: '<EMAIL>', to: @user_email, subject: '[VEPT] Xác nhận nộp lệ phí thi thành công')
  end
end
