# Hướng dẫn tích hợp Sepay

## Tổng quan
Dự án đã được tích hợp với Sepay để xử lý thanh toán tự động cho đăng ký dự thi. Khi thí sinh đăng ký, hệ thống sẽ tạo QR Code thanh toán và khi Sepay xác nhận thanh toán thành công qua webhook, trạng thái đăng ký sẽ được cập nhật tự động.

## Cấu hình

### 1. Cập nhật thông tin Sepay
Dự án sử dụng gem `config` để quản lý cấu hình. Chỉnh sửa các file sau:

**File `config/settings/production.yml`:**
```yaml
sepay:
  api_token: "YOUR_PRODUCTION_SEPAY_API_TOKEN_HERE"  # Thay bằng API token thực từ Sepay
  webhook_url: "https://sea-edu.com.vn/sepay/webhook"
  debug: false
```

**File `config/settings/development.yml`:**
```yaml
sepay:
  api_token: "YOUR_DEV_SEPAY_API_TOKEN_HERE"  # Token cho development
  webhook_url: "http://localhost:3000/sepay/webhook"
  debug: true
```

### 2. Cấu hình Webhook trên Sepay
1. Đăng nhập vào tài khoản Sepay: https://my.sepay.vn
2. Vào menu **WebHooks** → **+ Thêm webhooks**
3. Cấu hình như sau:
   - **Đặt tên**: VEPT Payment Webhook
   - **Chọn sự kiện**: Có tiền vào
   - **Khi tài khoản ngân hàng là**: Chọn tài khoản Agribank của bạn
   - **Bỏ qua nếu nội dung giao dịch không có Code thanh toán**: Không
   - **Gọi đến URL**: `https://sea-edu.com.vn/sepay/webhook`
   - **Là WebHooks xác thực thanh toán**: Đúng
   - **Kiểu chứng thực**: Không cần chứng thực

### 3. Cài đặt gems và chạy migration
```bash
bundle install
rails db:migrate
```

### 4. Kiểm tra cấu hình
```bash
# Mở Rails console
rails console

# Kiểm tra settings
Settings.sepay.api_token
Settings.sepay.account_number

# Test Sepay service
sepay_service = SepayService.new
```

## Cách hoạt động

### Flow đăng ký mới:
1. Thí sinh điền form đăng ký
2. Hệ thống tạo record với `payment_status = 'pending'`
3. Tạo QR Code thanh toán với nội dung: `VEPT YYYYMMDDXX PHONE_NUMBER FULLNAME`
4. Hiển thị QR Code và thông tin chuyển khoản cho thí sinh
5. Gửi email thông báo đăng ký thành công

### Flow thanh toán:
1. Thí sinh quét QR Code hoặc chuyển khoản thủ công
2. Sepay phát hiện giao dịch và gửi webhook đến `/sepay/webhook`
3. Hệ thống xử lý webhook:
   - Tìm exam registration form dựa trên nội dung chuyển khoản
   - Cập nhật `payment_status = 'paid'`
   - Lưu `sepay_transaction_id` và `payment_confirmed_at`
   - Gửi email xác nhận thanh toán thành công

### Admin Panel:
- Thêm filter theo trạng thái thanh toán
- Hiển thị badge trạng thái thanh toán trong bảng danh sách
- Xuất Excel bao gồm thông tin thanh toán

## Các trạng thái thanh toán:
- **pending**: Chờ thanh toán (màu vàng)
- **paid**: Đã thanh toán (màu xanh)
- **failed**: Thanh toán thất bại (màu đỏ)

## Email templates:
- `registration_confirmation`: Email thông báo đăng ký thành công
- `payment_confirmation`: Email xác nhận thanh toán thành công

## Bảo mật:
- Webhook chỉ chấp nhận request từ IP của Sepay: `*************`
- Trong development, cho phép thêm `127.0.0.1` và `::1`

## Troubleshooting:

### Kiểm tra log webhook:
```bash
tail -f log/production.log | grep "Sepay webhook"
```

### Test webhook trong development:
1. Sử dụng ngrok để expose localhost
2. Cập nhật webhook URL trên Sepay
3. Tạo giao dịch giả lập trên Sepay

### Kiểm tra trạng thái thanh toán:
```ruby
# Rails console
exam_form = ExamRegistrationForm.find(ID)
exam_form.payment_status
exam_form.sepay_transaction_id
exam_form.payment_confirmed_at

# Test tạo QR Code
sepay_service = SepayService.new
payment_info = sepay_service.generate_payment_qr(exam_form)
puts payment_info[:qr_url]
# Kết quả: https://qr.sepay.vn/img?acc=*************&bank=Agribank&amount=1600000&des=VEPT...
```
